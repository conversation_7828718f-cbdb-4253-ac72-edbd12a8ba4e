import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import { AuthService } from '../services/auth.service';

@Component({
  selector: 'app-admin-login',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './admin-login.html',
  styleUrl: './admin-login.css'
})
export class AdminLoginComponent implements OnInit {

  private returnUrl: string = '/dashboard';
  isLoading: boolean = false;

  constructor(
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    console.log('🔐 Admin Login Component Initialized');

    // Get return URL from route parameters or default to dashboard
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';
    console.log('🎯 Return URL set to:', this.returnUrl);

    // Check current authentication state
    const isAuth = this.authService.isAuthenticated();
    const currentAdmin = this.authService.getCurrentAdmin();
    console.log('🔍 Current auth state on init:', isAuth);
    console.log('👤 Current admin on init:', currentAdmin);

    // If already authenticated, redirect to dashboard
    if (isAuth && currentAdmin) {
      console.log('✅ Already authenticated, redirecting to:', this.returnUrl);
      this.router.navigate([this.returnUrl]);
    } else {
      console.log('❌ Not authenticated, showing login form');
    }
  }

  // Handle email input
  onEmailInput(event: Event): void {
    const input = event.target as HTMLInputElement;
    this.hideEmailError();
  }

  // Handle email blur validation
  onEmailBlur(event: Event): void {
    const input = event.target as HTMLInputElement;
    this.validateEmail(input);
  }

  // Validate email format
  validateEmail(input: HTMLInputElement): void {
    const value = input.value.trim();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (!value) {
      this.showEmailError('Email is required!');
      input.classList.add('border-red-500');
      input.classList.remove('border-gray-200');
    } else if (!emailRegex.test(value)) {
      this.showEmailError('Please enter a valid email address!');
      input.classList.add('border-red-500');
      input.classList.remove('border-gray-200');
    } else {
      this.hideEmailError();
      input.classList.remove('border-red-500');
      input.classList.add('border-gray-200');
    }
  }

  // Show email error
  showEmailError(message: string): void {
    const errorDiv = document.getElementById('emailError');
    if (errorDiv) {
      errorDiv.textContent = message;
      errorDiv.classList.remove('hidden');
    }
  }

  // Hide email error
  hideEmailError(): void {
    const errorDiv = document.getElementById('emailError');
    if (errorDiv) {
      errorDiv.classList.add('hidden');
    }
  }

  // Handle password input
  onPasswordInput(event: Event): void {
    this.hidePasswordError();
  }

  // Handle password blur validation
  onPasswordBlur(event: Event): void {
    const input = event.target as HTMLInputElement;
    this.validatePassword(input);
  }

  // Validate password
  validatePassword(input: HTMLInputElement): void {
    const value = input.value.trim();
    
    if (!value) {
      this.showPasswordError('Password is required!');
      input.classList.add('border-red-500');
      input.classList.remove('border-gray-200');
    } else {
      this.hidePasswordError();
      input.classList.remove('border-red-500');
      input.classList.add('border-gray-200');
    }
  }

  // Show password error
  showPasswordError(message: string): void {
    const errorDiv = document.getElementById('passwordError');
    if (errorDiv) {
      errorDiv.textContent = message;
      errorDiv.classList.remove('hidden');
    }
  }

  // Hide password error
  hidePasswordError(): void {
    const errorDiv = document.getElementById('passwordError');
    if (errorDiv) {
      errorDiv.classList.add('hidden');
    }
  }

  // Handle form submission
  onSubmit(event: Event): void {
    event.preventDefault();

    if (this.isLoading) return;

    const emailInput = document.getElementById('email') as HTMLInputElement;
    const passwordInput = document.getElementById('password') as HTMLInputElement;

    let isValid = true;

    // Always validate both fields to show individual error messages
    this.validateEmail(emailInput);
    this.validatePassword(passwordInput);

    // Check if both fields are valid
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailInput.value.trim() || !emailRegex.test(emailInput.value.trim())) {
      isValid = false;
    }

    if (!passwordInput.value.trim()) {
      isValid = false;
    }

    if (isValid) {
      this.isLoading = true;
      const email = emailInput.value.trim();
      const password = passwordInput.value.trim();

      console.log('🔐 Attempting login with:', email, 'to redirect to:', this.returnUrl);

      this.authService.adminLogin(email, password).subscribe({
        next: (success: boolean) => {
          this.isLoading = false;
          console.log('📨 Login response received, success:', success);

          if (success) {
            console.log('✅ Admin login successful - redirecting to dashboard');
            this.debugAuthFlow();

            // Force refresh authentication state and then navigate
            this.authService.refreshAuthState();

            // Add a small delay to ensure authentication state is fully updated
            setTimeout(() => {
              console.log('🔄 === DELAYED NAVIGATION DEBUG ===');
              this.debugAuthFlow();

              this.router.navigate([this.returnUrl]).then(
                (navigated: boolean) => {
                  console.log('🧭 Navigation result:', navigated);
                  if (!navigated) {
                    console.error('❌ Navigation failed - trying alternative route');
                    this.debugAuthFlow();
                    // Try alternative navigation
                    window.location.href = this.returnUrl;
                  } else {
                    console.log('✅ Navigation successful!');
                  }
                }
              ).catch((error) => {
                console.error('❌ Navigation error:', error);
                this.debugAuthFlow();
                // Fallback to direct navigation
                window.location.href = this.returnUrl;
              });
            }, 200); // Increased delay to ensure auth state is updated
          } else {
            console.log('❌ Login failed - showing error message');
            this.showLoginError('Invalid email or password. Please try again.');
          }
        },
        error: (error) => {
          this.isLoading = false;
          console.error('❌ Login error:', error);
          this.showLoginError('Login failed. Please check your connection and try again.');
        }
      });
    }
  }

  // Show general login error
  showLoginError(message: string): void {
    const errorDiv = document.getElementById('loginError');
    if (errorDiv) {
      errorDiv.textContent = message;
      errorDiv.classList.remove('hidden');

      // Auto-hide after 5 seconds
      setTimeout(() => {
        errorDiv.classList.add('hidden');
      }, 5000);
    }
  }

  // Debug method to trace authentication flow
  debugAuthFlow(): void {
    console.log('🔍 === AUTHENTICATION DEBUG INFO ===');
    console.log('🔐 Is Authenticated:', this.authService.isAuthenticated());
    console.log('👤 Current Admin:', this.authService.getCurrentAdmin());
    console.log('🎯 Return URL:', this.returnUrl);
    console.log('💾 LocalStorage Admin:', localStorage.getItem('currentAdmin'));
    console.log('💾 LocalStorage Token:', localStorage.getItem('adminToken'));
    console.log('🌐 Current URL:', window.location.href);
    console.log('📍 Router URL:', this.router.url);
    console.log('=================================');

    // Test manual navigation
    console.log('🧪 Testing manual navigation to dashboard...');
    this.router.navigate(['/dashboard']).then(
      (success) => {
        console.log('🧭 Manual navigation result:', success);
      }
    ).catch(
      (error) => {
        console.error('❌ Manual navigation error:', error);
      }
    );
  }

  // Test method to simulate successful authentication
  testSuccessfulAuth(): void {
    console.log('🧪 === TESTING SUCCESSFUL AUTHENTICATION ===');

    // Manually set authentication data in localStorage
    const testAdmin = {
      adminId: 1,
      fullName: 'Test Admin',
      email: '<EMAIL>',
      role: 'Super Admin',
      status: 'Active'
    };

    localStorage.setItem('currentAdmin', JSON.stringify(testAdmin));
    localStorage.setItem('adminToken', 'test-token-123');

    // Force refresh auth state
    this.authService.refreshAuthState();

    console.log('✅ Test authentication data set');
    this.debugAuthFlow();

    // Try navigation
    setTimeout(() => {
      console.log('🧭 Attempting navigation after test auth...');
      this.router.navigate(['/dashboard']);
    }, 100);
  }
}
