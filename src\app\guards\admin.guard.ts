import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AdminGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {

    console.log('🛡️ Admin guard checking access to:', state.url);
    console.log('🔐 Current auth state:', this.authService.isAuthenticated());
    console.log('👤 Current admin:', this.authService.getCurrentAdmin());

    return this.authService.validateSession().pipe(
      map((isValid: boolean) => {
        console.log('✅ Session validation result:', isValid);

        if (isValid && this.authService.isAuthenticated()) {
          const currentAdmin = this.authService.getCurrentAdmin();
          console.log('👤 Current admin from guard:', currentAdmin);

          if (currentAdmin) {
            console.log(`✅ Admin access granted for: ${currentAdmin.fullName} (${currentAdmin.role})`);
            return true;
          } else {
            console.log('❌ No current admin found');
          }
        } else {
          console.log('❌ Session invalid or not authenticated');
        }

        console.log('❌ Admin access denied - redirecting to login');
        this.router.navigate(['/adminlogin'], {
          queryParams: { returnUrl: state.url }
        });
        return false;
      }),
      catchError((error) => {
        console.error('❌ Admin guard error:', error);
        this.router.navigate(['/adminlogin'], {
          queryParams: { returnUrl: state.url }
        });
        return of(false);
      })
    );
  }
}
